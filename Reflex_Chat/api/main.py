from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from sqlmodel import select, Session
from sqlalchemy.orm import selectinload
from datetime import datetime
from typing import List
import logging
import sys

# Configure root logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Create loggers for different modules
api_logger = logging.getLogger("api")
service_logger = logging.getLogger("service")
db_logger = logging.getLogger("database")

# Set log levels
api_logger.setLevel(logging.INFO)
service_logger.setLevel(logging.INFO)
db_logger.setLevel(logging.INFO)

# Database
from Reflex_Chat.database.db import get_session
from Reflex_Chat.database.models import User, Project, UserProject, Role

# Routes
from Reflex_Chat.api.routes.user_summary_route import app as user_summary_router
from Reflex_Chat.api.routes.score_calculator_route import app as score_calculator_router
from Reflex_Chat.api.routes.rag_route import app as rag_router

app = FastAPI(
    title="Reflex Chat API",
    description="API for Reflex Chat application",
    version="1.0.0"
)

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup."""
    api_logger.info("Starting up FastAPI application")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # Reflex frontend
        "http://localhost:8000",  # Reflex backend
        "http://reflex:3000",     # Docker network
        "http://reflex:8000"      # Docker network
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(user_summary_router, tags=["users"])
app.include_router(score_calculator_router, tags=["scores"])
app.include_router(rag_router, tags=["rag"])

@app.get("/")
async def root():
    api_logger.info("Root endpoint accessed")
    return {"message": "Welcome to Reflex Chat API"}
