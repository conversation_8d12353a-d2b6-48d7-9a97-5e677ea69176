import reflex as rx
import logging
from Reflex_Chat.state import AuthState
from sqlmodel import select
from typing import List, Dict, Optional
from Reflex_Chat.components.navbar import navbar
from Reflex_Chat.components.background import background_v3
from Reflex_Chat.database.db import get_session
from Reflex_Chat.database.models import (
    Competency,
    CompetencyCategory,
    CompetencyRoleMap,
    EvaluationQuestion,
    User,
    Project,
    Evaluation,
    EvaluationAnswer,
    EvaluationType,
    EvaluatorType,
    EvaluationStatus,
    ResponseType,
)
from datetime import datetime, timezone
# Update this import to use the new path
from Reflex_Chat.database.utils.score_calculator import (
    calculate_competency_scores,
    calculate_factor_scores_for_evaluation,
    calculate_category_scores_for_evaluation
)

#helper data structures
class Question(rx.Base):
    id: int
    text: str

#for a competency and its questions
class CompetencyData(rx.Base):
    id: int
    name: str
    description: str
    questions: List[Question]
    category: Optional[str] = None

#central STATE class
class PerformanceEvalFormState(AuthState):
    """Local state for the performance evaluation form."""

    #key parameters for question selection
    project_id: str = ""
    evaluatee_id: str = ""
    role_id: int = 0

    #key parameters for UI navigation
    factor_steps: List[str] = []  # Ordered list of unique factors
    current_step_index: int = 0

    # New navigation structure for factor-based navigation
    navigation_items: List[Dict] = []  # List of navigation items (factors + competencies + action plans)
    current_navigation_index: int = 0

    # Initialize empty dictionaries for competencies
    competencies_by_factor: Dict[str, List[CompetencyData]] = {}
    competencies_by_category: Dict[str, List[CompetencyData]] = {}

    #key parameters for response storage
    responses: Dict[int, str] = {}  # question_id -> response ("yes", "no", "sometimes")
    competency_comments: Dict[int, str] = {}  # competency_id -> comment
    temp_selections: Dict[int, str] = {}  # Temporary selections for UI

    # Action plan state
    action_plans: Dict[str, str] = {}  # factor -> action_plan_text
    current_action_plan_text: str = ""  # Current action plan being edited
    show_action_plan_input: bool = False  # Whether to show action plan input
    is_main_evaluator: bool = False  # Whether current user can create action plans

    #title parameters
    evaluatee_name: str = ""
    project_name: str = ""

    # Single source of truth for category colors
    # Maps both display names and internal keys to the same color values
    # Using class variable for access from both instance and class methods
    CATEGORY_COLORS = {
        # Display names (with spaces and capitalization)
        "Competencias Comportamentales": "crimson",
        "Competencias Tecnicas": "violet",
        "Aprendizaje": "indigo",
        "Feedback Cliente": "crimson",
        "Feedback Manager": "grass",

        # Internal keys (snake_case)
        "competencias_comportamentales": "crimson",
        "competencias_tecnicas": "violet",
        "aprendizaje": "indigo",
        "feedback_cliente": "crimson",
        "feedback_manager": "grass",

        # Default
        "default": "orange"
    }

    #on load, get parameters from URL and load competencies
    async def on_load(self):
        """Load initial data."""
        # Get query parameters
        query_params = self.router.page.params
        self.project_id = query_params.get("project_id", "")
        self.evaluatee_id = query_params.get("evaluatee_id", "")

        # Debug print to verify IDs
        print(f"Loading evaluation - Project ID: {self.project_id}, Evaluatee ID: {self.evaluatee_id}")
        print(f"Current user ID: {self.db_user_id}")

        # Check if current user is a main evaluator
        await self.check_main_evaluator_status()

        # Load competencies and questions
        self.load_competencies()

    #fetches the evaluatee's role, gets competencies for the role, and groups them by factor
    def load_competencies(self):
        # Check if evaluatee_id and project_id are valid
        if not self.evaluatee_id or not self.project_id:
            print(f"Invalid evaluatee_id or project_id: {self.evaluatee_id}, {self.project_id}")
            return

        try:
            with get_session() as session:
                evaluatee = session.get(User, int(self.evaluatee_id))
                if not evaluatee:
                    print(f"Evaluatee not found with ID: {self.evaluatee_id}")
                    return

                self.evaluatee_name = evaluatee.name

                project = session.get(Project, int(self.project_id))
                if project:
                    self.project_name = project.name

                self.role_id = evaluatee.role_id

                # Modified query to get competencies through role mapping
                stmt = (
                    select(Competency)
                    .join(CompetencyRoleMap)
                    .where(CompetencyRoleMap.role_id == self.role_id)
                    .order_by(Competency.code)  # Sort by competency code for consistent ordering
                )
                competencies = session.exec(stmt).all()

                competencies_by_factor: Dict[str, List[CompetencyData]] = {}
                competencies_by_category: Dict[str, List[CompetencyData]] = {}
                factor_order: List[str] = []
                category_order: List[str] = []

                for comp in competencies:
                    # Get questions without the self/peer evaluation filters
                    questions = session.exec(
                        select(EvaluationQuestion)
                        .where(EvaluationQuestion.competency_id == comp.id)
                    ).all()

                    if questions:  # Only add competencies that have questions
                        # Get category display name
                        category = comp.category.value if comp.category else "Otro"
                        category_display = category.replace("_", " ").title()

                        comp_data = CompetencyData(
                            id=comp.id,
                            name=comp.name,
                            description=comp.description,
                            questions=[Question(id=q.id, text=q.text) for q in questions],
                            category=category_display
                        )

                        # Group by factor
                        factor = comp.factor or "Otro"
                        if factor not in competencies_by_factor:
                            competencies_by_factor[factor] = []
                            factor_order.append(factor)
                        competencies_by_factor[factor].append(comp_data)

                        # Group by category (using the category from comp_data)
                        if comp_data.category not in competencies_by_category:
                            competencies_by_category[comp_data.category] = []
                            category_order.append(comp_data.category)
                        competencies_by_category[comp_data.category].append(comp_data)

                # Sort competencies within each factor by ID for consistent ordering
                for factor in competencies_by_factor:
                    competencies_by_factor[factor].sort(key=lambda comp: comp.id)

                # Sort competencies within each category by ID for consistent ordering
                for category in competencies_by_category:
                    competencies_by_category[category].sort(key=lambda comp: comp.id)

                self.competencies_by_factor = competencies_by_factor
                self.competencies_by_category = competencies_by_category
                self.factor_steps = factor_order
                self.current_step_index = 0
                self.current_competency_index = 0

                # Build the new navigation structure
                self.build_navigation_structure()

                # Set initial navigation position to first competency
                self.set_initial_navigation_position()

                # Debug print
                print(f"Loaded {len(competencies)} competencies for role {self.role_id}")
                print(f"Factors: {factor_order}")
                print(f"Navigation items: {len(self.navigation_items)}")
                for factor, comps in competencies_by_factor.items():
                    print(f"Factor {factor}: {len(comps)} competencies")
                    # Print competency details for debugging
                    for i, comp in enumerate(comps):
                        print(f"  {i+1}. ID: {comp.id}, Name: {comp.name}")

                print(f"Categories: {category_order}")
                for category, comps in competencies_by_category.items():
                    print(f"Category {category}: {len(comps)} competencies")

        except ValueError as e:
            print(f"Error converting IDs to integers: {e}")
            return

    def build_navigation_structure(self):
        """Build the hierarchical navigation structure with factors, competencies, and action plans."""
        self.navigation_items = []

        for factor_index, factor in enumerate(self.factor_steps):
            competencies = self.competencies_by_factor.get(factor, [])

            # Get the category for this factor (from first competency)
            category = competencies[0].category if competencies else "Otro"

            # Add factor header item
            factor_item = {
                "type": "factor",
                "factor": factor,
                "category": category,
                "factor_index": factor_index,
                "name": factor,
                "is_header": True
            }
            self.navigation_items.append(factor_item)

            # Add competency items under this factor
            for comp_index, competency in enumerate(competencies):
                comp_item = {
                    "type": "competency",
                    "factor": factor,
                    "category": category,
                    "factor_index": factor_index,
                    "competency_index": comp_index,
                    "competency_id": competency.id,
                    "name": competency.name,
                    "is_header": False
                }
                self.navigation_items.append(comp_item)

            # Add action plan item if user is main evaluator
            if self.is_main_evaluator:
                action_plan_item = {
                    "type": "action_plan",
                    "factor": factor,
                    "category": category,
                    "factor_index": factor_index,
                    "name": f"{factor} - Plan de Acción",
                    "is_header": False
                }
                self.navigation_items.append(action_plan_item)

    def set_initial_navigation_position(self):
        """Set the initial navigation position to the first competency."""
        # Find the first competency item (skip factor headers)
        for i, item in enumerate(self.navigation_items):
            if item["type"] == "competency":
                self.current_navigation_index = i
                break
        else:
            # If no competency found, start at 0
            self.current_navigation_index = 0

    #manages users responses temporary storage
    def set_temp_selection(self, question_id: int, response: str):
        """Set temporary selection for UI."""
        # If clicking the same response, clear it
        if self.temp_selections.get(question_id) == response:
            self.temp_selections.pop(question_id, None)
        else:
            self.temp_selections[question_id] = response

    def get_temp_selection(self, question_id: int) -> Optional[str]:
        """Get temporary selection for UI."""
        return self.temp_selections.get(question_id)

    def is_selected(self, question_id: int, response: str) -> bool:
        """Check if a specific response is selected for a question."""
        return self.temp_selections.get(question_id) == response

    async def get_current_user_id(self) -> Optional[int]:
        """Get the current user's ID from the auth state."""
        # Get the current state instead of instantiating a new one
        return self.db_user_id

    async def check_main_evaluator_status(self):
        """Check if the current user is a main evaluator."""
        user_id = await self.get_current_user_id()
        if user_id:
            with get_session() as session:
                user = session.get(User, user_id)
                if user:
                    self.is_main_evaluator = user.main_evaluator
                    print(f"User {user_id} main_evaluator status: {self.is_main_evaluator}")

    def set_action_plan_text(self, text: str):
        """Set the current action plan text."""
        self.current_action_plan_text = text

    def save_current_action_plan(self):
        """Save the current action plan for the current factor."""
        if not self.factor_steps or self.current_step_index >= len(self.factor_steps):
            return

        current_factor = self.factor_steps[self.current_step_index]
        self.action_plans[current_factor] = self.current_action_plan_text
        self.show_action_plan_input = False
        print(f"Saved action plan for factor {current_factor}: {self.current_action_plan_text[:50]}...")

    def show_action_plan_for_current_factor(self):
        """Show action plan input for the current factor."""
        if not self.is_main_evaluator:
            return

        if not self.factor_steps or self.current_step_index >= len(self.factor_steps):
            return

        current_factor = self.factor_steps[self.current_step_index]
        # Load existing action plan if any
        self.current_action_plan_text = self.action_plans.get(current_factor, "")
        self.show_action_plan_input = True

    def cancel_action_plan_input(self):
        """Cancel action plan input without saving."""
        self.show_action_plan_input = False
        self.current_action_plan_text = ""

    def set_comment(self, competency_id: int, comment: str):
        """Set comment for a specific competency."""
        self.competency_comments[competency_id] = comment

    def get_competency_comment(self, competency_id: int) -> str:
        """Get comment for a specific competency."""
        return self.competency_comments.get(competency_id, "")

    async def submit_evaluation(self):
        """Submit the evaluation and calculate scores."""
        # First check if all questions are answered
        if not self.are_all_questions_answered():
            return rx.window_alert("Debes responder todas las preguntas antes de enviar la evaluación.")

        self.responses.update(self.temp_selections)

        user_id = await self.get_current_user_id()
        if user_id is None:
            return rx.redirect("/login")

        with get_session() as session:
            try:
                # Check if this is a self-evaluation
                is_self_evaluation = str(self.db_user_id) == self.evaluatee_id

                evaluation = Evaluation(
                    evaluator_id=user_id,
                    evaluatee_id=int(self.evaluatee_id),
                    project_id=int(self.project_id),
                    evaluation_type=EvaluationType.PERFORMANCE,
                    evaluator_type=EvaluatorType.SELF if is_self_evaluation else EvaluatorType.PEER,
                    status=EvaluationStatus.SUBMITTED,
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc),
                    evaluatee_role_id=self.role_id  # Store the evaluatee's role at evaluation time
                )
                session.add(evaluation)
                session.flush()  # Get the evaluation ID

                print(f"Created evaluation with ID: {evaluation.id}")

                # Save answers for all factors
                for factor in self.factor_steps:
                    competencies = self.competencies_by_factor.get(factor, [])
                    for comp in competencies:
                        for question in comp.questions:
                            if question.id in self.responses:
                                # Convert string response to ResponseType enum
                                # "yes" -> ResponseType.SI, "no" -> ResponseType.NO, "sometimes" -> ResponseType.A_VECES
                                response_value = ResponseType.A_VECES  # Default
                                if self.responses[question.id] == "yes":
                                    response_value = ResponseType.SI
                                elif self.responses[question.id] == "no":
                                    response_value = ResponseType.NO

                                answer = EvaluationAnswer(
                                    evaluation_id=evaluation.id,
                                    question_id=question.id,
                                    response=response_value,
                                    comment=self.competency_comments.get(comp.id)
                                )
                                session.add(answer)

                session.flush()  # Ensure all answers are saved
                print("Saved all answers")

                # Calculate and save competency scores
                competency_scores = calculate_competency_scores(evaluation.id, session)
                session.add_all(competency_scores)
                session.flush()
                print(f"Saved {len(competency_scores)} competency scores")

                # Calculate and save factor scores
                factor_scores = calculate_factor_scores_for_evaluation(evaluation.id, session)
                if factor_scores:
                    session.add_all(factor_scores)
                    session.flush()
                    print(f"Saved {len(factor_scores)} factor scores")
                else:
                    print("No factor scores calculated")

                # Calculate and save category scores
                category_scores = calculate_category_scores_for_evaluation(evaluation.id, session)
                if category_scores:
                    session.add_all(category_scores)
                    session.flush()
                    print(f"Saved {len(category_scores)} category scores")
                else:
                    print("No category scores calculated")

                # Save action plans if user is main evaluator
                if self.is_main_evaluator and self.action_plans:
                    from Reflex_Chat.database.models import ActionPlan, CompetencyCategory

                    for factor, action_plan_text in self.action_plans.items():
                        if action_plan_text.strip():  # Only save non-empty action plans
                            # Find the category for this factor
                            category = None
                            for comp_factor, competencies in self.competencies_by_factor.items():
                                if comp_factor == factor and competencies:
                                    # Get category from first competency in this factor
                                    category_str = competencies[0].category.lower().replace(" ", "_")
                                    try:
                                        category = CompetencyCategory(category_str)
                                        break
                                    except ValueError:
                                        print(f"Invalid category for factor {factor}: {category_str}")
                                        continue

                            if category:
                                action_plan = ActionPlan(
                                    evaluation_id=evaluation.id,
                                    category=category,
                                    factor=factor,
                                    action_plan_text=action_plan_text,
                                    evaluator_id=user_id,
                                    created_at=datetime.now(timezone.utc)
                                )
                                session.add(action_plan)

                    session.flush()
                    print(f"Saved {len(self.action_plans)} action plans")

                # Final commit
                session.commit()
                print("Successfully committed all changes")

                # Clear state
                self.temp_selections.clear()
                self.responses.clear()
                self.competency_comments.clear()
                self.action_plans.clear()
                self.current_action_plan_text = ""
                self.show_action_plan_input = False

                return rx.redirect("/evaluations/performance")

            except Exception as e:
                logging.error(f"Error submitting evaluation: {str(e)}")
                session.rollback()
                return rx.window_alert("Error submitting evaluation. Please try again.")

    def is_current_factor_complete(self) -> bool:
        """Check if all questions in the current factor have been answered."""
        current_factor = self.factor_steps[self.current_step_index]
        competencies = self.competencies_by_factor.get(current_factor, [])

        for competency in competencies:
            for question in competency.questions:
                # Check if the question has any selection ("yes", "no", or "sometimes")
                if question.id not in self.temp_selections:
                    return False
        return True

    def are_all_questions_answered(self) -> bool:
        """Check if all questions across all factors have been answered."""
        # Count total questions and answered questions for debugging
        total_questions = 0
        answered_questions = 0

        # Iterate through all factors
        for factor in self.factor_steps:
            competencies = self.competencies_by_factor.get(factor, [])

            # Iterate through all competencies in this factor
            for competency in competencies:
                # Iterate through all questions in this competency
                for question in competency.questions:
                    total_questions += 1

                    # Check if the question has any selection
                    if question.id not in self.temp_selections:
                        print(f"Question {question.id} ({question.text[:30]}...) is not answered")
                        return False
                    else:
                        answered_questions += 1

        # Log the counts for debugging
        print(f"All questions answered: {answered_questions}/{total_questions}")

        # If we've checked all questions and none are missing answers, return True
        return True

    ##### Competency UI #####
    current_competency_index: int = 0

    @rx.var
    def current_competency(self) -> Optional[CompetencyData]:
        if not self.factor_steps or self.current_step_index >= len(self.factor_steps):
            return None

        current_factor = self.factor_steps[self.current_step_index]
        competencies = self.competencies_by_factor.get(current_factor, [])
        if competencies and 0 <= self.current_competency_index < len(competencies):
            return competencies[self.current_competency_index]
        return None

    @rx.var
    def current_category(self) -> Optional[str]:
        """Get the category of the current competency."""
        comp = self.current_competency
        if comp:
            return comp.category
        return None

    @rx.var
    def is_last_competency(self) -> bool:
        """Check if we're at the last navigation item."""
        return self.current_navigation_index >= len(self.navigation_items) - 1

    @rx.var
    def is_first_competency(self) -> bool:
        """Check if we're at the first navigation item."""
        return self.current_navigation_index <= 0

    def next_competency(self):
        """Move to the next item in the navigation structure."""
        if self.current_navigation_index < len(self.navigation_items) - 1:
            self.current_navigation_index += 1
            next_item = self.navigation_items[self.current_navigation_index]

            if next_item["type"] == "competency":
                self.current_step_index = next_item["factor_index"]
                self.current_competency_index = next_item["competency_index"]
                self.show_action_plan_input = False
            elif next_item["type"] == "action_plan":
                self.current_step_index = next_item["factor_index"]
                self.show_action_plan_for_current_factor()
            elif next_item["type"] == "factor":
                # Skip factor headers, go to next item
                self.next_competency()
        else:
            # At the end, stay at current position
            pass

    def prev_competency(self):
        """Move to the previous item in the navigation structure."""
        if self.current_navigation_index > 0:
            self.current_navigation_index -= 1
            prev_item = self.navigation_items[self.current_navigation_index]

            if prev_item["type"] == "competency":
                self.current_step_index = prev_item["factor_index"]
                self.current_competency_index = prev_item["competency_index"]
                self.show_action_plan_input = False
            elif prev_item["type"] == "action_plan":
                self.current_step_index = prev_item["factor_index"]
                self.show_action_plan_for_current_factor()
            elif prev_item["type"] == "factor":
                # Skip factor headers, go to previous item
                self.prev_competency()
        else:
            # At the beginning, stay at current position
            pass

    def next_step(self):
        """Move to the next step."""
        if self.current_step_index < len(self.factor_steps) - 1:
            self.current_step_index += 1

    def prev_step(self):
        """Move to the previous step."""
        if self.current_step_index > 0:
            self.current_step_index -= 1

    @rx.var
    def get_selection(self) -> Dict[int, Optional[str]]:
        return self.temp_selections

    @rx.var
    def total_progress(self) -> int:
        """Calculate progress based on navigation items."""
        if not self.navigation_items:
            return 0

        # Count only competency and action plan items (skip factor headers)
        navigable_items = [item for item in self.navigation_items if item["type"] != "factor"]

        if not navigable_items:
            return 0

        # Find current position among navigable items
        current_navigable_index = 0
        for i, item in enumerate(self.navigation_items):
            if item["type"] != "factor":
                if i <= self.current_navigation_index:
                    current_navigable_index += 1
                else:
                    break

        # Calculate progress as percentage
        progress = int((current_navigable_index / len(navigable_items)) * 100)
        return min(progress, 100)

    # Menu navigation methods

    def navigate_to_competency(self, factor: str, competency_index: int):
        """Navigate directly to a specific competency."""
        # Find the factor index
        if factor in self.factor_steps:
            factor_index = self.factor_steps.index(factor)
            self.current_step_index = factor_index

            # Set the competency index if valid
            competencies = self.competencies_by_factor.get(factor, [])
            if 0 <= competency_index < len(competencies):
                self.current_competency_index = competency_index

    def navigate_to_competency_by_id(self, competency_id: int):
        """Navigate directly to a specific competency by its ID."""
        # Find the competency in all factors
        for factor_index, factor in enumerate(self.factor_steps):
            competencies = self.competencies_by_factor.get(factor, [])
            for comp_index, comp in enumerate(competencies):
                if comp.id == competency_id:
                    self.current_step_index = factor_index
                    self.current_competency_index = comp_index
                    return
        # If competency not found, don't change anything
        return

    @rx.var
    def get_current_competency_id(self) -> int:
        """Get the ID of the current competency."""
        comp = self.current_competency
        if comp:
            return comp.id
        return -1

    # New navigation methods for hierarchical structure

    def navigate_to_item(self, item_index: int):
        """Navigate to a specific item in the navigation structure."""
        if 0 <= item_index < len(self.navigation_items):
            item = self.navigation_items[item_index]
            self.current_navigation_index = item_index

            if item["type"] == "competency":
                # Navigate to competency
                self.current_step_index = item["factor_index"]
                self.current_competency_index = item["competency_index"]
                self.show_action_plan_input = False
            elif item["type"] == "action_plan":
                # Navigate to action plan for this factor
                self.current_step_index = item["factor_index"]
                self.current_competency_index = 0  # Reset to first competency
                self.show_action_plan_for_current_factor()
            elif item["type"] == "factor":
                # Navigate to first competency of this factor
                self.current_step_index = item["factor_index"]
                self.current_competency_index = 0
                self.show_action_plan_input = False

    @rx.var
    def current_navigation_item(self) -> Optional[Dict]:
        """Get the current navigation item."""
        if 0 <= self.current_navigation_index < len(self.navigation_items):
            return self.navigation_items[self.current_navigation_index]
        return None

    def update_navigation_index_from_current_state(self):
        """Update the navigation index based on current step and competency indices."""
        # Find the navigation item that matches current state
        for i, item in enumerate(self.navigation_items):
            if item["type"] == "competency":
                if (item["factor_index"] == self.current_step_index and
                    item["competency_index"] == self.current_competency_index):
                    self.current_navigation_index = i
                    return
            elif item["type"] == "action_plan" and self.show_action_plan_input:
                if item["factor_index"] == self.current_step_index:
                    self.current_navigation_index = i
                    return

    @rx.var
    def has_competencies(self) -> bool:
        """Check if there are any competencies loaded."""
        return bool(self.competencies_by_category)

    # Class method for accessing colors from outside the class
    @classmethod
    def get_category_color_static(cls, category: Optional[str]) -> str:
        """Get the color for a category (class method).

        This is a class method that can be called from outside the class.
        It handles both display names and internal keys.
        """
        # Hard-coded mapping for direct lookup - more reliable than using class variables
        # in some contexts with Reflex
        category_colors = {
            # Display names (with spaces and capitalization)
            "Competencias Comportamentales": "crimson",
            "Competencias Tecnicas": "violet",
            "Aprendizaje": "indigo",
            "Feedback Cliente": "crimson",
            "Feedback Manager": "grass",

            # Internal keys (snake_case)
            "competencias_comportamentales": "crimson",
            "competencias_tecnicas": "violet",
            "aprendizaje": "indigo",
            "feedback_cliente": "crimson",
            "feedback_manager": "grass",

            # Default
            "default": "orange"
        }

        if not category:
            return "orange"  # Default color

        # Convert to string to ensure we're working with a plain string
        category_str = str(category)

        # First try direct lookup (handles both display names and snake_case)
        if category_str in category_colors:
            return category_colors[category_str]

        # If not found, try converting display name to snake_case
        category_key = category_str.lower().replace(" ", "_")
        return category_colors.get(category_key, "orange")

    # Instance method for use within the class
    def get_category_color(self, category: Optional[str]) -> str:
        """Get the color for a category (instance method).

        This is the central method for retrieving category colors.
        It handles both display names and internal keys.
        """
        return self.__class__.get_category_color_static(category)

    # For backward compatibility and clarity, keep this method
    def get_category_color_scheme(self, category: Optional[str]) -> str:
        """Get the color scheme for a category.

        This is an alias for get_category_color for clarity when used with
        color_scheme attributes in Reflex components.
        """
        return self.get_category_color(category)

    # Class method alias for color_scheme
    @classmethod
    def get_category_color_scheme_static(cls, category: Optional[str]) -> str:
        """Get the color scheme for a category (class method).

        This is an alias for get_category_color_static for clarity when used with
        color_scheme attributes in Reflex components.
        """
        # Use the same implementation for consistency and reliability
        return cls.get_category_color_static(category)

    @rx.var
    def current_category_color(self) -> str:
        """Get the color for the current category."""
        # Handle the case when current_category is None
        if self.current_category is None:
            return "orange"  # Default color

        # Convert to string to ensure we're working with a plain string
        category_str = str(self.current_category)

        # Use direct lookup in the dictionary for better reliability
        if category_str in self.CATEGORY_COLORS:
            return self.CATEGORY_COLORS[category_str]

        # Try with snake_case conversion
        category_key = category_str.lower().replace(" ", "_")
        if category_key in self.CATEGORY_COLORS:
            return self.CATEGORY_COLORS[category_key]

        # Default fallback
        return "orange"

    @rx.var
    def current_category_color_scheme(self) -> str:
        """Get the color scheme for the current category."""
        # Use the same implementation as current_category_color for consistency
        return self.current_category_color

    @rx.var
    def current_factor(self) -> str:
        """Get the current factor name."""
        if not self.factor_steps or self.current_step_index >= len(self.factor_steps):
            return ""
        return self.factor_steps[self.current_step_index]

def render_action_plan_input() -> rx.Component:
    """Render the action plan input component."""

    return rx.card(
        rx.vstack(
            rx.heading(
                "Plan de Acción para ",
                PerformanceEvalFormState.current_factor,
                size="5",
                mb="4",
                color="blue.600"
            ),
            rx.text(
                "Como evaluador principal, puedes crear un plan de acción para este factor. "
                "Este plan será visible para el evaluado y le ayudará a mejorar en esta área.",
                size="3",
                color="gray.600",
                mb="4"
            ),
            rx.text_area(
                placeholder="Escribe aquí el plan de acción para este factor...",
                value=PerformanceEvalFormState.current_action_plan_text,
                on_change=PerformanceEvalFormState.set_action_plan_text,
                width="100%",
                height="200px",
                resize="vertical"
            ),
            rx.hstack(
                rx.button(
                    "Cancelar",
                    on_click=PerformanceEvalFormState.cancel_action_plan_input,
                    color_scheme="gray",
                    variant="outline"
                ),
                rx.spacer(),
                rx.button(
                    "Guardar Plan de Acción",
                    on_click=PerformanceEvalFormState.save_current_action_plan,
                    color_scheme="blue"
                ),
                width="100%",
                spacing="3"
            ),
            spacing="4",
            width="100%"
        ),
        width="100%",
        padding="6"
    )

@rx.page(
    route="/evaluations/performance/start",
    title="Iniciar Evaluación de Desempeño",
    on_load=PerformanceEvalFormState.on_load,
)
def performance_evaluation_start_page() -> rx.Component:

    #renders a question and its responses
    def render_question(question):
        question_id = question.id

        return rx.card(
            rx.flex(
                rx.text(
                    question.text,
                    size="3",
                    font_weight="medium",
                    line_height="1.5",
                    flex="1",
                    pr="4",
                ),
                rx.flex(
                    rx.button(
                        "Sí",
                        on_click=lambda qid=question_id: PerformanceEvalFormState.set_temp_selection(qid, "yes"),
                        color_scheme=rx.cond(
                            PerformanceEvalFormState.get_selection[question_id] == "yes",
                            "green",
                            "gray"
                        ),
                        variant=rx.cond(
                            PerformanceEvalFormState.get_selection[question_id] == "yes",
                            "solid",
                            "outline"
                        ),
                        width="60px",
                        height="36px",
                        font_weight="bold",
                        font_size="sm",
                        line_height="1",
                        padding_x="0",
                        border_width="2px",
                        radius="full",
                        _hover={"opacity": 0.9},
                    ),
                    rx.button(
                        "A veces",
                        on_click=lambda qid=question_id: PerformanceEvalFormState.set_temp_selection(qid, "sometimes"),
                        color_scheme="gray",
                        variant=rx.cond(
                            PerformanceEvalFormState.get_selection[question_id] == "sometimes",
                            "solid",
                            "outline"
                        ),
                        width="80px",
                        height="36px",
                        font_weight="bold",
                        font_size="sm",
                        line_height="1",
                        padding_x="0",
                        border_width="2px",
                        radius="full",
                        _hover={"opacity": 0.9},
                    ),
                    rx.button(
                        "No",
                        on_click=lambda qid=question_id: PerformanceEvalFormState.set_temp_selection(qid, "no"),
                        color_scheme=rx.cond(
                            PerformanceEvalFormState.get_selection[question_id] == "no",
                            "red",
                            "gray"
                        ),
                        variant=rx.cond(
                            PerformanceEvalFormState.get_selection[question_id] == "no",
                            "solid",
                            "outline"
                        ),
                        width="60px",
                        height="36px",
                        font_weight="bold",
                        font_size="sm",
                        line_height="1",
                        padding_x="0",
                        border_width="2px",
                        radius="full",
                        _hover={"opacity": 0.9},
                    ),
                    spacing="3",
                    align="center",
                ),
                direction="row",
                spacing="3",
                width="100%",
                align="center",
            ),
            padding="3",
            border_radius="lg",
            box_shadow="sm",
            overflow="auto",
            style={"transition": "all 0.2s ease"},
            _hover={"transform": "translateY(-3px)", "boxShadow": "0 4px 8px rgba(0, 0, 0, 0.1)"},
            width="100%",
        )

    def render_competency(competency):
        return rx.flex(
            rx.hstack(
                rx.text(
                    PerformanceEvalFormState.current_factor,
                    " - Competencia ",
                    PerformanceEvalFormState.current_competency_index + 1,
                    ": ",
                    competency.name,
                    size="5"
                ),
                rx.spacer(),
                width="100%",
            ),
            rx.text(competency.description, color="gray", size="3"),
            rx.vstack(
                rx.foreach(competency.questions, render_question),
                spacing="4",
            ),
            rx.text_area(
                placeholder="Comentario (opcional)...",
                value=rx.cond(
                    PerformanceEvalFormState.competency_comments.get(competency.id),
                    PerformanceEvalFormState.competency_comments[competency.id],
                    ""
                ),
                on_change=lambda val, cid=competency.id: PerformanceEvalFormState.set_comment(cid, val),
                width="100%",
                mt="4",
            ),
            direction="column",
            justify="between",
            spacing="4",
            margin="1em auto 0 auto",
        )

    # Sidebar menu component
    def sidebar_menu():
        """Render a hierarchical sidebar menu with factors, competencies, and action plans."""

        def get_category_color(category: str) -> str:
            """Get color for a category."""
            category_colors = {
                "Competencias Comportamentales": "crimson",
                "Competencias Tecnicas": "violet",
                "Aprendizaje": "indigo",
                "Feedback Cliente": "crimson",
                "Feedback Manager": "grass"
            }
            return category_colors.get(category, "orange")

        def render_navigation_item(item, index):
            """Render a single navigation item."""
            category_color = get_category_color(item.get("category", ""))
            is_current = index == PerformanceEvalFormState.current_navigation_index

            if item.get("type") == "factor":
                # Factor header
                return rx.box(
                    rx.badge(
                        item.get("name", ""),
                        color_scheme=category_color,
                        variant="solid",
                        size="2",
                        border_radius="md",
                        width="100%",
                        text_align="center"
                    ),
                    width="100%",
                    margin_y="2",
                    padding_x="2"
                )
            else:
                # Competency or action plan item
                return rx.box(
                    rx.button(
                        rx.hstack(
                            rx.cond(
                                item.get("type") == "action_plan",
                                rx.text("📋", margin_right="2"),
                                rx.text("")
                            ),
                            rx.text(
                                item.get("name", ""),
                                text_overflow="ellipsis",
                                overflow="hidden",
                                white_space="nowrap",
                                flex="1"
                            ),
                            spacing="1",
                            align_items="center",
                            width="100%"
                        ),
                        on_click=lambda idx=index: PerformanceEvalFormState.navigate_to_item(idx),
                        variant="ghost",
                        size="1",
                        width="100%",
                        justify_content="flex-start",
                        padding_y="1",
                        padding_x="3",
                        border_radius="md",
                        text_align="left",
                        background=rx.cond(
                            is_current,
                            rx.color(category_color, 3),
                            "transparent"
                        ),
                        border_left=rx.cond(
                            is_current,
                            f"6px solid {rx.color(category_color, 9)}",
                            "6px solid transparent"
                        ),
                        color=rx.cond(
                            is_current,
                            rx.color(category_color, 11),
                            rx.color("gray", 11)
                        ),
                        font_weight=rx.cond(
                            is_current,
                            "bold",
                            "normal"
                        ),
                        _hover={
                            "background": rx.color(category_color, 2),
                            "color": rx.color(category_color, 11)
                        },
                    ),
                    width="100%",
                    padding_left="4" if item.get("type") != "factor" else "0"
                )

        return rx.box(
            rx.vstack(
                rx.cond(
                    PerformanceEvalFormState.navigation_items.length() > 0,
                    rx.box(
                        rx.vstack(
                            rx.foreach(
                                PerformanceEvalFormState.navigation_items,
                                lambda item, index: render_navigation_item(item, index)
                            ),
                            width="100%",
                            spacing="1",
                            align_items="stretch",
                            padding="2",
                        ),
                        width="100%",
                        overflow_y="auto",
                        flex="1",
                        padding="2",
                    ),
                    rx.box(
                        rx.vstack(
                            rx.text(
                                "No hay competencias disponibles.",
                                color=rx.color("gray", 9),
                                size="2",
                                text_align="center",
                                margin_top="8",
                            ),
                            rx.text(
                                "Por favor, selecciona un proyecto y un usuario para evaluar.",
                                color=rx.color("gray", 9),
                                size="2",
                                text_align="center",
                            ),
                            width="100%",
                            spacing="2",
                            align_items="center",
                            padding="4",
                        ),
                        width="100%",
                        overflow_y="auto",
                        padding="2",
                    ),
                ),
                width="100%",
                height="100%",
                spacing="0",
                align_items="stretch",
            ),
            width="510px",
            height="calc(100vh - 4rem)",
            overflow_x="hidden",
            overflow_y="auto",
            background_color=rx.color("mauve", 1),
            position="sticky",
            top="0",
        )

    # We'll use PerformanceEvalFormState.current_competency directly

    # Get color scheme for the current category using the centralized method
    # This is a reactive variable that will update when current_category changes
    category_color_scheme = PerformanceEvalFormState.current_category_color_scheme

    return rx.box(
        navbar(),
        #background_v3(),
        rx.flex(
            # Left sidebar menu - takes full height of left column
            sidebar_menu(),

            # Right column with stacked content and navigation
            rx.flex(
                # Main content area
                rx.box(
                    rx.container(
                        rx.vstack(
                            # rx.hstack(
                            #     rx.cond(
                            #         PerformanceEvalFormState.current_competency != None,
                            #         rx.badge(
                            #             rx.text(
                            #                 PerformanceEvalFormState.current_category,
                            #                 font_size="4",
                            #                 font_weight="bold",
                            #             ),
                            #             size="1",
                            #             variant="solid",
                            #             high_contrast=False,
                            #             color_scheme=category_color_scheme,
                            #             radius="large",
                            #             text_transform="capitalize",
                            #         ),
                            #         rx.text("")  # Empty text if no competency
                            #     ),
                            #     width="100%",
                            #     display="flex",
                            #     justify_content="flex-start",
                            #     align_items="center",
                            #     spacing="3",
                            # ),
                            #rx.spacer(),
                            rx.heading(f"Evalua el desempeño de {PerformanceEvalFormState.evaluatee_name}, en el proyecto {PerformanceEvalFormState.project_name}:", size="6", mb="4"),
                            width="100%",
                            align_items="start",
                            mb="4",
                        ),
                        rx.cond(
                            PerformanceEvalFormState.show_action_plan_input,
                            render_action_plan_input(),
                            rx.cond(
                                PerformanceEvalFormState.current_competency != None,
                                render_competency(PerformanceEvalFormState.current_competency),
                            ),
                        ),
                        padding="3%",
                        #margin="2em auto 0 auto",  # Reduced top margin
                        overflow_y="auto",
                    ),
                    width="100%",
                    flex="1",  # Take remaining space
                    overflow_y="auto",
                ),

                # Navigation controls - now part of the right column stack
                rx.box(
                    rx.container(
                        rx.hstack(
                            rx.button(
                                "Anterior",
                                on_click=PerformanceEvalFormState.prev_competency,
                                is_disabled=PerformanceEvalFormState.is_first_competency,
                                width="100px",
                                color_scheme=category_color_scheme,
                            ),
                            rx.spacer(),
                            rx.progress(
                                value=PerformanceEvalFormState.total_progress,
                                width="30%",
                                color_scheme=category_color_scheme,
                            ),
                            rx.spacer(),
                            rx.cond(
                                PerformanceEvalFormState.show_action_plan_input,
                                # Action plan input navigation - buttons are in the component
                                rx.text(""),
                                rx.cond(
                                    PerformanceEvalFormState.is_last_competency,
                                    # Show submit button at the last navigation item
                                    rx.cond(
                                        PerformanceEvalFormState.are_all_questions_answered(),
                                        rx.button(
                                            "Enviar",
                                            on_click=PerformanceEvalFormState.submit_evaluation,
                                            width="100px",
                                            color_scheme="green",
                                        ),
                                        # Otherwise show a disabled button with tooltip
                                        rx.tooltip(
                                            rx.button(
                                                "Enviar",
                                                is_disabled=True,
                                                width="100px",
                                                color_scheme="gray",
                                            ),
                                            content="Debes responder todas las preguntas antes de enviar",
                                            placement="center",
                                        ),
                                    ),
                                    # Show the Siguiente button if not at the last item
                                    rx.button(
                                        "Siguiente",
                                        on_click=PerformanceEvalFormState.next_competency,
                                        width="100px",
                                        color_scheme=category_color_scheme,
                                    ),
                                ),
                            ),
                            spacing="6",
                            align_items="center",
                            height="100%",
                        ),
                        height="100%",
                    ),
                    height="80px",
                    background=rx.color("mauve", 1),
                    #border_top="1px solid #E2E8F0",
                    display="flex",
                    align_items="center",
                    justify_content="center",
                    width="100%",
                    position="sticky",
                    bottom="0",
                ),
                direction="column",  # Stack vertically in the right column
                width="100%",
                height="calc(100vh - 4rem)",  # Full height minus navbar
                overflow="hidden",
            ),
            direction="row",  # Two columns side by side
            spacing="1",
            align_items="flex-start",  # Align items at the top
            width="100%",
            height="calc(100vh - 4rem)",  # Full height minus navbar
        ),
        width="100%",
        height="100vh",
        overflow="hidden",
    )
