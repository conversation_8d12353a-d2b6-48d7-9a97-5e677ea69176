from ..db import get_session
import json
import os
from dotenv import load_dotenv
from sqlmodel import Session
from ..db import engine
from ..models import *
from ..models import BehaviorType  # Explicitly import BehaviorType
from datetime import datetime
import random
from sqlalchemy import and_
from ..utils.score_calculator import (
    calculate_competency_scores,
    calculate_factor_scores_for_evaluation,
    calculate_category_scores_for_evaluation
)

# Load environment variables
load_dotenv()

def seed_roles():
    roles = ["<PERSON><PERSON><PERSON>", "Consultor", "Manager", "Director", "<PERSON>cio"]
    with get_session() as session:
        for role_name in roles:
            if not session.query(Role).filter_by(name=role_name).first():
                session.add(Role(name=role_name))
        session.commit()
    print("Roles seeded.")

def seed_performance_competencies():
    with open("assets/competencias_performance.json", "r", encoding="utf-8") as f:
        data = json.load(f)

    with get_session() as session:
        for comp in data:
            if not session.query(Competency).filter_by(code=str(comp["code"])).first():
                # Map the JSON category to the CompetencyCategory enum
                category_map = {
                    "competencias_tecnicas": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "competencias_comportamentales": CompetencyCategory.COMPETENCIAS_COMPORTAMENTALES,
                    "feedback_cliente": CompetencyCategory.FEEDBACK_CLIENTE,
                    "feedback_manager": CompetencyCategory.FEEDBACK_MANAGER,
                    "aprendizaje": CompetencyCategory.APRENDIZAJE
                }

                # Convert the category to lowercase and remove any spaces
                category_key = comp["categoria"].lower().strip()
                category = category_map.get(category_key)

                if not category:
                    print(f"[WARNING] Unknown category: {comp['categoria']} for competency {comp['code']}")
                    continue

                new_comp = Competency(
                    code=str(comp["code"]),
                    name=comp["name"],
                    description=comp.get("description", ""),
                    factor=comp.get("factor"),
                    group=comp.get("group"),
                    category=category
                )
                session.add(new_comp)
                session.flush()

                # Process expert behavior questions
                for q in comp.get("expert_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.EXPERT
                        )
                        session.add(question)

                # Process talented behavior questions
                for q in comp.get("talented_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.TALENTED
                        )
                        session.add(question)

                # Process low skill behavior questions
                for q in comp.get("low_skill_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.LOW_SKILL
                        )
                        session.add(question)
        session.commit()
    print("Technical competencies seeded.")

def seed_potential_competencies():
    with open("assets/competencias_potencial.json", "r", encoding="utf-8") as f:
        data = json.load(f)

    with get_session() as session:
        for comp in data:
            if not session.query(Competency).filter_by(code=str(comp["code"])).first():
                category_map = {
                    "competencias_tecnicas": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "competencias_comportamentales": CompetencyCategory.COMPETENCIAS_COMPORTAMENTALES,
                    "feedback_cliente": CompetencyCategory.FEEDBACK_CLIENTE,
                    "feedback_manager": CompetencyCategory.FEEDBACK_MANAGER,
                    "aprendizaje": CompetencyCategory.APRENDIZAJE
                }

                category_key = comp["categoria"].lower().strip()
                category = category_map.get(category_key)

                if not category:
                    print(f"[WARNING] Unknown category: {comp['categoria']} for competency {comp['code']}")
                    continue

                new_comp = Competency(
                    code=str(comp["code"]),
                    name=comp["name"],
                    description=comp.get("description", ""),
                    factor=comp.get("factor"),
                    group=comp.get("group"),
                    category=category
                )
                session.add(new_comp)
                session.flush()

                # Process expert behavior questions
                for q in comp.get("expert_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.EXPERT
                        )
                        session.add(question)

                # Process talented behavior questions
                for q in comp.get("talented_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.TALENTED
                        )
                        session.add(question)

                # Process low skill behavior questions
                for q in comp.get("low_skill_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.LOW_SKILL
                        )
                        session.add(question)
        session.commit()
    print("Potential competencies seeded.")

def seed_competency_role_map_from_json():
    with open("assets/role_comp_map.json", "r", encoding="utf-8") as f:
        role_comp_map = json.load(f)

    with get_session() as session:
        for entry in role_comp_map:
            role = session.query(Role).filter_by(name=entry["role"]).first()
            if not role:
                print(f"[WARNING] Role not found: {entry['role']}")
                continue

            for comp_code in entry["competencies"]:
                competency = session.query(Competency).filter_by(code=str(comp_code)).first()
                if not competency:
                    print(f"[WARNING] Competency code not found: {comp_code}")
                    continue

                exists = session.query(CompetencyRoleMap).filter_by(
                    role_id=role.id,
                    competency_id=competency.id
                ).first()

                if not exists:
                    session.add(CompetencyRoleMap(
                        role_id=role.id,
                        competency_id=competency.id,
                        weight=1.0
                    ))

        session.commit()
    print("CompetencyRoleMap seeded from JSON.")

def seed_users_and_projects():
    # Get user Azure ID from environment variable or use a default
    user_azure_id = os.getenv("USER_AZURE_ID")

    users_data = [
        {"name": "Bruno Bolla Pons", "email": "<EMAIL>", "azure_id": user_azure_id, "role_name": "Analista", "main_evaluator": True},
        {"name": "Ana García López", "email": "<EMAIL>", "azure_id": "00000000-0000-0000-0000-000000000001", "role_name": "Analista", "main_evaluator": False},
        {"name": "Carlos Ruiz Martínez", "email": "<EMAIL>", "azure_id": "00000000-0000-0000-0000-000000000002", "role_name": "Consultor", "main_evaluator": True},
        {"name": "Laura Torres Jiménez", "email": "<EMAIL>", "azure_id": "00000000-0000-0000-0000-000000000005", "role_name": "Analista", "main_evaluator": False},
        {"name": "Javier Moreno Rodríguez", "email": "<EMAIL>", "azure_id": "00000000-0000-0000-0000-000000000006", "role_name": "Consultor", "main_evaluator": True},
    ]

    projects_data = [
        {"code": "PRJ001", "name": "AI Strategy", "start_date": datetime(2024, 1, 1), "status": ProjectStatus.ACTIVE},
        {"code": "PRJ002", "name": "Digital Transformation", "start_date": datetime(2024, 2, 15), "status": ProjectStatus.ACTIVE},
    ]

    user_project_links = [
        #p1
        ("<EMAIL>", "PRJ001"),
        ("<EMAIL>", "PRJ001"),
        ("<EMAIL>", "PRJ001"),

        # Project 2 (Digital Transformation)
        ("<EMAIL>", "PRJ002"),
        ("<EMAIL>", "PRJ002"),
        ("<EMAIL>", "PRJ002"),
    ]

    with get_session() as session:
        # First ensure we have roles
        roles = session.query(Role).all()
        if not roles:
            print("Warning: No roles found. Please run seed_roles() first.")
            return

        # Create users with roles
        for user_data in users_data:
            if not session.query(User).filter_by(email=user_data["email"]).first():
                role = session.query(Role).filter_by(name=user_data["role_name"]).first()
                if not role:
                    print(f"Warning: Role '{user_data['role_name']}' not found for user {user_data['email']}")
                    continue

                role_name = user_data.pop("role_name")
                user_data["role_id"] = role.id

                session.add(User(**user_data))
        session.commit()

        # Create projects
        for proj in projects_data:
            if not session.query(Project).filter_by(code=proj["code"]).first():
                session.add(Project(**proj))
        session.commit()

        # Create user-project links
        for email, code in user_project_links:
            user = session.query(User).filter_by(email=email).first()
            project = session.query(Project).filter_by(code=code).first()
            if user and project:
                if not session.query(UserProject).filter_by(user_id=user.id, project_id=project.id).first():
                    session.add(UserProject(user_id=user.id, project_id=project.id))
        session.commit()

def seed_evaluations():
    with get_session() as session:
        # Fetch all required data upfront
        users = session.query(User).all()
        projects = session.query(Project).all()
        roles = session.query(Role).all()

        batch_size = 100  # Process evaluations in batches
        current_batch = []

        # Get user 1 specifically
        user_1 = session.query(User).filter_by(id=1).first()
        if user_1:
            print(f"Creating diverse evaluations for user: {user_1.name} (ID: {user_1.id})")

            # Get competencies mapped to user_1's role
            user_1_competencies = (
                session.query(Competency)
                .join(CompetencyRoleMap)
                .where(CompetencyRoleMap.role_id == user_1.role_id)
                .all()
            )

            # Get questions only for user_1's role competencies
            questions = (
                session.query(EvaluationQuestion)
                .join(Competency)
                .join(CompetencyRoleMap)
                .where(CompetencyRoleMap.role_id == user_1.role_id)
                .all()
            )

            # Group questions by competency_id and behavior_type for faster access
            questions_by_competency = {}
            for q in questions:
                if q.competency_id not in questions_by_competency:
                    questions_by_competency[q.competency_id] = []
                questions_by_competency[q.competency_id].append(q)

            # Create evaluations for user 1 for each project
            for project in projects:
                print(f"Creating evaluation for project: {project.name} (ID: {project.id})")

                # Create self-evaluation for user 1 for this project with varied responses
                self_eval = Evaluation(
                    evaluation_type=EvaluationType.PERFORMANCE,
                    evaluator_type=EvaluatorType.SELF,
                    evaluator_id=user_1.id,
                    evaluatee_id=user_1.id,
                    project_id=project.id,
                    status=EvaluationStatus.DRAFT if random.random() < 0.5 else EvaluationStatus.SUBMITTED,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    evaluatee_role_id=user_1.role_id  # Add evaluatee's role at evaluation time
                )
                session.add(self_eval)
                session.flush()  # Get the ID

                # Create answers with varied responses based on behavior type
                answers = []
                for question in questions:
                    # Determine response based on behavior type
                    if question.behavior_type == BehaviorType.EXPERT:
                        # Expert behavior - 70% positive
                        rand = random.random()
                        if rand < 0.7:
                            response = ResponseType.SI
                        elif rand < 0.85:  # 15% sometimes
                            response = ResponseType.A_VECES
                        else:  # 15% no
                            response = ResponseType.NO
                    elif question.behavior_type == BehaviorType.TALENTED:
                        # Talented behavior - 50% positive
                        rand = random.random()
                        if rand < 0.5:
                            response = ResponseType.SI
                        elif rand < 0.7:  # 20% sometimes
                            response = ResponseType.A_VECES
                        else:  # 30% no
                            response = ResponseType.NO
                    else:
                        # Low skill behavior - 30% positive
                        rand = random.random()
                        if rand < 0.3:
                            response = ResponseType.SI
                        elif rand < 0.6:  # 30% sometimes
                            response = ResponseType.A_VECES
                        else:  # 40% no
                            response = ResponseType.NO

                    answer = EvaluationAnswer(
                        evaluation_id=self_eval.id,
                        question_id=question.id,
                        response=response,
                        comment=f"Self-evaluation for {user_1.name} on project {project.name}: {question.text[:50]}..."
                    )
                    answers.append(answer)

                session.bulk_save_objects(answers)
                current_batch.append(self_eval.id)

                # Create peer evaluations from other users to user 1 with varied patterns
                project_users = [
                    up.user for up in session.query(UserProject)
                    .filter_by(project_id=project.id)
                    .all() if up.user.id != user_1.id
                ]

                # Create peer evaluations with different patterns for each peer
                for idx, peer in enumerate(project_users[:3]):  # Increase to 3 peers per project
                    peer_eval = Evaluation(
                        evaluation_type=EvaluationType.PERFORMANCE,
                        evaluator_type=EvaluatorType.PEER,
                        evaluator_id=peer.id,
                        evaluatee_id=user_1.id,
                        project_id=project.id,
                        status=EvaluationStatus.SUBMITTED,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow(),
                        evaluatee_role_id=user_1.role_id  # Add evaluatee's role at evaluation time
                    )
                    session.add(peer_eval)
                    session.flush()

                    # Create answers with different patterns for each peer
                    peer_answers = []
                    for question in questions:
                        # Adjust response probability based on peer's role
                        peer_role = session.query(Role).filter_by(id=peer.role_id).first()
                        if peer_role.name in ["Analista", "Consultor"]:
                            # Junior roles - better at technical, worse at leadership
                            if question.competency.category == CompetencyCategory.COMPETENCIAS_TECNICAS:
                                # 85% positive
                                rand = random.random()
                                if rand < 0.85:
                                    response = ResponseType.SI
                                elif rand < 0.95:  # 10% sometimes
                                    response = ResponseType.A_VECES
                                else:  # 5% no
                                    response = ResponseType.NO
                            else:
                                # 50% positive
                                rand = random.random()
                                if rand < 0.5:
                                    response = ResponseType.SI
                                elif rand < 0.7:  # 20% sometimes
                                    response = ResponseType.A_VECES
                                else:  # 30% no
                                    response = ResponseType.NO
                        else:
                            # Senior roles - better at leadership, mixed at technical
                            if question.competency.category == CompetencyCategory.COMPETENCIAS_COMPORTAMENTALES:
                                # 90% positive
                                rand = random.random()
                                if rand < 0.9:
                                    response = ResponseType.SI
                                elif rand < 0.95:  # 5% sometimes
                                    response = ResponseType.A_VECES
                                else:  # 5% no
                                    response = ResponseType.NO
                            else:
                                # 70% positive
                                rand = random.random()
                                if rand < 0.7:
                                    response = ResponseType.SI
                                elif rand < 0.85:  # 15% sometimes
                                    response = ResponseType.A_VECES
                                else:  # 15% no
                                    response = ResponseType.NO

                        answer = EvaluationAnswer(
                            evaluation_id=peer_eval.id,
                            question_id=question.id,
                            response=response,
                            comment=f"Peer evaluation from {peer.name} for {user_1.name} on {question.text[:50]}..."
                        )
                        peer_answers.append(answer)

                    session.bulk_save_objects(peer_answers)
                    current_batch.append(peer_eval.id)

                # Process batch if it reaches the batch size
                if len(current_batch) >= batch_size:
                    session.commit()
                    process_evaluation_batch(current_batch, session)
                    current_batch = []

        # Generate random evaluations for other users
        for project in projects:
            project_users = [
                up.user for up in session.query(UserProject)
                .filter_by(project_id=project.id)
                .all() if up.user.id != 1  # Skip user 1 as we've already created custom evals
            ]

            for user in project_users:
                # Get competencies mapped to user's role
                user_competencies = (
                    session.query(Competency)
                    .join(CompetencyRoleMap)
                    .where(CompetencyRoleMap.role_id == user.role_id)
                    .all()
                )

                # Get questions only for user's role competencies
                user_questions = (
                    session.query(EvaluationQuestion)
                    .join(Competency)
                    .join(CompetencyRoleMap)
                    .where(CompetencyRoleMap.role_id == user.role_id)
                    .all()
                )

                # Self evaluation (50% chance)
                if random.random() < 0.50:
                    self_eval = Evaluation(
                        evaluation_type=EvaluationType.PERFORMANCE,
                        evaluator_type=EvaluatorType.SELF,
                        evaluator_id=user.id,
                        evaluatee_id=user.id,
                        project_id=project.id,
                        status=EvaluationStatus.DRAFT if random.random() < 0.5 else EvaluationStatus.SUBMITTED,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow(),
                        evaluatee_role_id=user.role_id  # Add evaluatee's role at evaluation time
                    )
                    session.add(self_eval)
                    session.flush()  # Get the ID

                    # Create all answers for this evaluation at once
                    answers = []
                    for question in user_questions:
                        # 80% positive, 10% sometimes, 10% no
                        rand = random.random()
                        if rand < 0.8:
                            response = ResponseType.SI
                        elif rand < 0.9:
                            response = ResponseType.A_VECES
                        else:
                            response = ResponseType.NO

                        answer = EvaluationAnswer(
                            evaluation_id=self_eval.id,
                            question_id=question.id,
                            response=response,
                            comment=f"Self-evaluation comment for {user.name} on {question.text}"
                        )
                        answers.append(answer)

                    session.bulk_save_objects(answers)
                    current_batch.append(self_eval.id)

                    # Process batch if it reaches the batch size
                    if len(current_batch) >= batch_size:
                        session.commit()
                        process_evaluation_batch(current_batch, session)
                        current_batch = []

        # Process any remaining evaluations
        if current_batch:
            session.commit()
            process_evaluation_batch(current_batch, session)

    print("Evaluations and scores seeded with diverse patterns.")

def process_evaluation_batch(eval_ids: List[int], session: Session):
    """Process a batch of evaluations to calculate their scores."""
    for eval_id in eval_ids:
        try:
            # Calculate all scores in one transaction
            comp_scores = calculate_competency_scores(eval_id, session)
            session.bulk_save_objects(comp_scores)

            factor_scores = calculate_factor_scores_for_evaluation(eval_id, session)
            if factor_scores:
                session.bulk_save_objects(factor_scores)

            category_scores = calculate_category_scores_for_evaluation(eval_id, session)
            if category_scores:
                session.bulk_save_objects(category_scores)

            session.commit()
            print(f"✓ Processed scores for evaluation {eval_id}")

        except Exception as e:
            print(f"✗ Error processing scores for evaluation {eval_id}: {str(e)}")
            session.rollback()
            continue

if __name__ == "__main__":
    seed_roles()
    seed_performance_competencies()
    seed_potential_competencies()
    seed_competency_role_map_from_json()
    seed_users_and_projects()
    seed_evaluations()
