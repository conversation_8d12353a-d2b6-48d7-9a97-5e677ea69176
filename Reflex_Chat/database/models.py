from sqlmodel import S<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Relationship
from typing import Optional, List
from datetime import datetime
from enum import Enum
from sqlalchemy import UniqueConstraint

# -------------------------------
# User & Role
# -------------------------------

class Role(SQLModel, table=True):
    """Defines the user's current role (e.g., <PERSON><PERSON><PERSON>, Manager)."""
    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(max_length=100)

    # Relationships
    users: List["User"] = Relationship(back_populates="role")

class User(SQLModel, table=True):
    """Represents an authenticated user with Azure ID and metadata."""
    id: Optional[int] = Field(default=None, primary_key=True)
    azure_id: str = Field(index=True, unique=True, max_length=255)
    name: str = Field(max_length=255)
    email: str = Field(max_length=255)
    last_login: Optional[datetime] = Field(default_factory=datetime.utcnow)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    is_active: bool = Field(default=True)
    role_id: Optional[int] = Field(default=None, foreign_key="role.id")

    # Relationships
    role: Optional[Role] = Relationship(back_populates="users")
    project_links: List["UserProject"] = Relationship(back_populates="user")
    evaluations_given: List["Evaluation"] = Relationship(
        back_populates="evaluator",
        sa_relationship_kwargs={'foreign_keys': '[Evaluation.evaluator_id]'}  # Fix: Use string path
    )
    evaluations_received: List["Evaluation"] = Relationship(
        back_populates="evaluatee",
        sa_relationship_kwargs={'foreign_keys': '[Evaluation.evaluatee_id]'}  # Fix: Use string path
    )

# -------------------------------
# Projects
# -------------------------------

class ProjectStatus(str, Enum):
    ACTIVE = "active"
    COMPLETED = "completed"
    ON_HOLD = "on-hold"

class Project(SQLModel, table=True):
    """Represents a project."""
    id: Optional[int] = Field(default=None, primary_key=True)
    code: str = Field(max_length=50)
    name: str = Field(max_length=255)
    start_date: datetime
    status: ProjectStatus

    # Relationships
    user_links: List["UserProject"] = Relationship(back_populates="project")
    evaluations: List["Evaluation"] = Relationship(back_populates="project")

class UserProject(SQLModel, table=True):
    """Links users and projects."""
    __tablename__ = "user_project"

    user_id: int = Field(foreign_key="user.id", primary_key=True)
    project_id: int = Field(foreign_key="project.id", primary_key=True)

    # Relationships
    user: User = Relationship(back_populates="project_links")
    project: Project = Relationship(back_populates="user_links")

# -------------------------------
# Competencies & Role Mapping
# -------------------------------

class CompetencyCategory(str, Enum):
    FEEDBACK_CLIENTE = "feedback_cliente"
    FEEDBACK_MANAGER = "feedback_manager"
    COMPETENCIAS_TECNICAS = "competencias_tecnicas"
    COMPETENCIAS_COMPORTAMENTALES = "competencias_comportamentales"
    APRENDIZAJE = "aprendizaje"

class Competency(SQLModel, table=True):
    """Defines a core competency with grouping for analysis and role mapping."""
    id: Optional[int] = Field(default=None, primary_key=True)
    code: str = Field(index=True, unique=True, max_length=50)
    name: str = Field(max_length=255)
    description: Optional[str]
    factor: Optional[str] = Field(default=None, max_length=255)
    group: Optional[str] = Field(default=None, max_length=255)
    category: CompetencyCategory
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    is_active: bool = Field(default=True)

    # Relationships
    role_mappings: List["CompetencyRoleMap"] = Relationship(back_populates="competency")
    questions: List["EvaluationQuestion"] = Relationship(back_populates="competency")
    scores: List["CompetencyScore"] = Relationship(back_populates="competency")

class CompetencyRoleMap(SQLModel, table=True):
    """Links a competency to a role (used for performance evals)."""
    id: Optional[int] = Field(default=None, primary_key=True)
    role_id: int = Field(foreign_key="role.id")
    competency_id: int = Field(foreign_key="competency.id")
    weight: Optional[float] = Field(default=1.0)

    # Relationships
    role: Role = Relationship()
    competency: Competency = Relationship(back_populates="role_mappings")

# -------------------------------
# Evaluations
# -------------------------------

class EvaluationType(str, Enum):
    PERFORMANCE = "performance"
    POTENTIAL = "potential"

class EvaluatorType(str, Enum):
    SELF = "self"
    PEER = "peer"

class EvaluationStatus(str, Enum):
    DRAFT = "draft"
    SUBMITTED = "submitted"
    REVIEWED = "reviewed"

class Evaluation(SQLModel, table=True):
    """Represents an evaluation instance."""
    id: Optional[int] = Field(default=None, primary_key=True)
    evaluator_id: int = Field(foreign_key="user.id")
    evaluatee_id: int = Field(foreign_key="user.id")
    project_id: Optional[int] = Field(default=None, foreign_key="project.id")
    evaluation_type: EvaluationType
    evaluator_type: EvaluatorType
    status: EvaluationStatus = Field(default=EvaluationStatus.DRAFT)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    evaluatee_role_id: Optional[int] = Field(default=None, foreign_key="role.id", description="The role of the evaluatee at the time of evaluation")

    # Relationships
    evaluator: User = Relationship(
        back_populates="evaluations_given",
        sa_relationship_kwargs={'foreign_keys': '[Evaluation.evaluator_id]'}
    )
    evaluatee: User = Relationship(
        back_populates="evaluations_received",
        sa_relationship_kwargs={'foreign_keys': '[Evaluation.evaluatee_id]'}
    )
    project: Optional[Project] = Relationship(back_populates="evaluations")
    evaluatee_role: Optional[Role] = Relationship(sa_relationship_kwargs={'foreign_keys': '[Evaluation.evaluatee_role_id]'})
    answers: List["EvaluationAnswer"] = Relationship(back_populates="evaluation")
    competency_scores: List["CompetencyScore"] = Relationship(back_populates="evaluation")
    category_scores: List["CategoryScore"] = Relationship()
    factor_scores: List["FactorScore"] = Relationship()

# -------------------------------
# Evaluation Questions & Answers
# -------------------------------

class BehaviorType(str, Enum):
    """Defines the type of behavior a question represents."""
    EXPERT = "expert"
    TALENTED = "talented"
    LOW_SKILL = "low_skill"

class ResponseType(str, Enum):
    """Defines the possible responses to evaluation questions."""
    SI = "si"  # Sí
    NO = "no"  # No
    A_VECES = "a_veces"  # A veces

class EvaluationQuestion(SQLModel, table=True):
    """Defines a yes/no question attached to a specific competency."""
    id: Optional[int] = Field(default=None, primary_key=True)
    text: str = Field(max_length=1000)
    competency_id: int = Field(foreign_key="competency.id")
    behavior_type: BehaviorType = Field(default=BehaviorType.EXPERT)

    # Relationships
    competency: Competency = Relationship(back_populates="questions")
    answers: List["EvaluationAnswer"] = Relationship(back_populates="question")

class EvaluationAnswer(SQLModel, table=True):
    """Stores a user's response to a question within an evaluation."""
    id: Optional[int] = Field(default=None, primary_key=True)
    evaluation_id: int = Field(foreign_key="evaluation.id")
    question_id: int = Field(foreign_key="evaluationquestion.id")
    response: ResponseType
    comment: Optional[str] = Field(default=None, max_length=1000)
    created_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)
    updated_at: Optional[datetime] = Field(default=None)

    # Relationships
    evaluation: Evaluation = Relationship(back_populates="answers")
    question: EvaluationQuestion = Relationship(back_populates="answers")

# -------------------------------
# Evaluation Score
# -------------------------------

class CompetencyScore(SQLModel, table=True):
    """Stores raw scores from individual evaluations at the competency level."""
    id: Optional[int] = Field(default=None, primary_key=True)
    evaluation_id: int = Field(foreign_key="evaluation.id", index=True)
    competency_id: int = Field(foreign_key="competency.id", index=True)
    score: float = Field(default=0.0, ge=0.0, le=100.0)
    comments: Optional[str] = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)
    updated_at: Optional[datetime] = Field(default=None)

    # Relationships
    evaluation: Evaluation = Relationship(back_populates="competency_scores")  # Changed from 'scores' to 'competency_scores'
    competency: Competency = Relationship(back_populates="scores")

class CategoryScore(SQLModel, table=True):
    """Stores category-level scores for an evaluation."""
    id: Optional[int] = Field(default=None, primary_key=True)
    evaluation_id: int = Field(foreign_key="evaluation.id", index=True)
    category: CompetencyCategory = Field(index=True)
    score: float = Field(default=0.0, ge=0.0, le=100.0)
    evaluation_count: int = Field(default=0, ge=0)
    comments: Optional[str] = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)
    updated_at: Optional[datetime] = Field(default=None)

    # Relationships
    evaluation: Evaluation = Relationship()

    class Config:
        """Add composite unique constraint."""
        schema_extra = {
            "unique_together": [("evaluation_id", "category")]
        }

class FactorScore(SQLModel, table=True):
    """Stores factor-level scores for an evaluation."""
    id: Optional[int] = Field(default=None, primary_key=True)
    evaluation_id: int = Field(foreign_key="evaluation.id", index=True)
    category: CompetencyCategory = Field(index=True)
    factor: str = Field(index=True, max_length=255)
    score: float = Field(default=0.0, ge=0.0, le=100.0)
    evaluation_count: int = Field(default=0, ge=0)
    comments: Optional[str] = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)
    updated_at: Optional[datetime] = Field(default=None)

    # Relationships
    evaluation: Evaluation = Relationship()

    class Config:
        """Add composite unique constraint."""
        schema_extra = {
            "unique_together": [("evaluation_id", "category", "factor")]
        }
