from sqlmodel import select
from Reflex_Chat.database.models import User, Evaluation, Competency, Role
from Reflex_Chat.database.db import get_session
from typing import List, Optional, Tuple
from datetime import datetime, timezone

# --- USER ---

def get_or_create_user(azure_id: str, name: str, email: str) -> Tuple[User, str]:
    print(f"\n=== User Authentication Process ===")
    print(f"[DEBUG] Attempting to get/create user with Azure ID: {azure_id}")
    print(f"[DEBUG] Name: {name}")
    print(f"[DEBUG] Email: {email}")

    with get_session() as session:
        # Just get the user without eager loading
        statement = select(User).where(User.azure_id == azure_id)
        user = session.exec(statement).first()

        if user:
            print(f"[DEBUG] Found existing user with ID: {user.id}")
            user.last_login = datetime.now(timezone.utc)
            session.add(user)
            session.commit()
            session.refresh(user)

            # Get role name
            role_name = "Unknown"
            if user.role_id:
                role = session.get(Role, user.role_id)
                if role:
                    role_name = role.name

            return user, role_name

        print("[DEBUG] No existing user found, creating new user")
        user = User(azure_id=azure_id, name=name, email=email)
        session.add(user)
        session.commit()
        session.refresh(user)
        print(f"[DEBUG] Created new user with ID: {user.id}")
        print("================================\n")

        # Get role name
        role_name = "Unknown"
        if user.role_id:
            role = session.get(Role, user.role_id)
            if role:
                role_name = role.name

        return user, role_name

# --- COMPETENCY ---

def get_or_create_competency(code: str, name: str, description: Optional[str] = None) -> Competency:
    with get_session() as session:
        comp = session.exec(select(Competency).where(Competency.code == code)).first()
        if comp:
            return comp
        comp = Competency(code=code, name=name, description=description)
        session.add(comp)
        session.commit()
        session.refresh(comp)
        return comp

# --- EVALUATIONS ---

def save_evaluation(
    user: User,
    competency: Competency,
    score: float,
    evaluator_type: str = "self",
    evaluator_email: Optional[str] = None,
    comments: Optional[str] = None
) -> Evaluation:
    with get_session() as session:
        eval = Evaluation(
            user_id=user.id,
            competency_id=competency.id,
            score=score,
            evaluator_type=evaluator_type,
            evaluator_email=evaluator_email,
            comments=comments,
            created_at=datetime.now(timezone.utc)
        )
        session.add(eval)
        session.commit()
        session.refresh(eval)
        return eval

def get_user_evaluations(user_id: int) -> List[Evaluation]:
    with get_session() as session:
        results = session.exec(
            select(Evaluation)
            .where(Evaluation.user_id == user_id)
            .order_by(Evaluation.created_at.desc())
        ).all()
        return results
